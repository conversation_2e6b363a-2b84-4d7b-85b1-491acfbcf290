<?php namespace Tests\Unit\Pdf\Overlays;

use Exception;
use Tests\TestCase;
use Upnovation\Easyprofile\Pdf\Overlays\ArrayMergeOverlay;

class ArrayMergeOverlayTest extends TestCase
{
    public function constructorProvider()
    {
        return [
            // Exception cases
            [Exception::class, [], []],
            [Exception::class, ['foo'], []],
            [Exception::class, ['keys' => 'not_array'], []],
            
            // Valid cases
            ["", ['keys' => ['foo']], []],
            ["", ['keys' => ['foo']], ['foo' => null]],
            ["", ['keys' => ['keyName']], ['badName' => 'keyvalue']],
            ['keyvalue', ['keys' => ['keyName']], ['keyName' => 'keyvalue']],
            ['', ['keys' => ['keyName']], ['keyName' => '']],
            [' ', ['keys' => ['keyName']], ['keyName' => ' ']],
            
            // Multiple keys
            ['value1 value2', ['keys' => ['key1', 'key2']], ['key1' => 'value1', 'key2' => 'value2']],
            ['value1value2', ['keys' => ['key1', 'key2'], 'separator' => ''], ['key1' => 'value1', 'key2' => 'value2']],
            ['value1/value2', ['keys' => ['key1', 'key2'], 'separator' => '/'], ['key1' => 'value1', 'key2' => 'value2']],
            
            // Multilevel keys
            ['baz qux', ['keys' => ['foo.bar', 'foo.baz']], ['foo' => ['bar' => 'baz', 'baz' => 'qux']]],
            
            // Mixed existing and non-existing keys
            ['value1', ['keys' => ['key1', 'nonexistent']], ['key1' => 'value1']],
            ['value2', ['keys' => ['nonexistent', 'key2']], ['key2' => 'value2']],
            
            // Empty values handling
            ['value2', ['keys' => ['key1', 'key2']], ['key1' => '', 'key2' => 'value2']],
            ['value1', ['keys' => ['key1', 'key2']], ['key1' => 'value1', 'key2' => null]],
        ];
    }

    /**
     * @dataProvider constructorProvider
     */
    public function testConstructor($expectation, $settings, $data)   
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        }

        $overlay = new ArrayMergeOverlay(1, 30, 90, $settings);
        $overlay->inject($data);

        $this->assertEquals($expectation, $overlay->getValue());
    }

    public function testRealWorldExample()
    {
        $data = [
            'beneficiarioDesignato1' => [
                'nome' => 'Mario',
                'cognome' => 'Rossi'
            ]
        ];

        $overlay = new ArrayMergeOverlay(1, 30, 90, [
            'keys' => ['beneficiarioDesignato1.nome', 'beneficiarioDesignato1.cognome'],
            'separator' => ' '
        ]);

        $overlay->inject($data);

        $this->assertEquals('Mario Rossi', $overlay->getValue());
    }
}
