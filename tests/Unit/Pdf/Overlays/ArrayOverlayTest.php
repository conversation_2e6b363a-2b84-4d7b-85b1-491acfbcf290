<?php namespace Tests\Unit\Pdf\Overlays;

use Exception;
use Tests\TestCase;
use TypeError;
use Upnovation\Easyprofile\Pdf\Overlays\ArrayOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;

class ArrayOverlayTest extends TestCase
{
    public function constructorProvider()
    {
        return [
            [Exception::class,  [],                         []],
            [Exception::class,  ['foo'],                    []],
            ["",                ['key' => 'foo'],           []],
            ["",                ['key' => 'foo'],           ['foo' => null]],
            ["",                ['key' => 'keyName'],       ['badName' => 'keyvalue']],
            ['keyvalue',        ['key' => 'keyName'],       ['keyName' => 'keyvalue']],
            ['',                ['key' => 'keyName'],       ['keyName' => '']],
            [' ',               ['key' => 'keyName'],       ['keyName' => ' ']],
            ['baz',             ['key' => 'foo.bar'],       ['foo' => ['bar' => 'baz']]],
            [TypeError::class,  ['key' => 'foo.'],          ['foo' => ['bar' => 'baz']]],
            ["",                ['key' => '.'],             ['foo' => ['bar' => 'baz']]],
            ["",                ['key' => '.bar'],          ['foo' => ['bar' => 'baz']]],
        ];
    }

    /**
     * @dataProvider constructorProvider
     */
    public function testConstructor($expectation, $settings, $data)   
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        }

        $overlay = new ArrayOverlay(1, 30, 90, $settings);

        $overlay->inject($data);

        $this->assertEquals($expectation, $overlay->getValue());
    }

    public function testMultiLevelPropertyResolution()
    {
        $settings = ['key' => 'foo.bar.baz'];
        $data = [ 'foo' => [ 'bar' => [ 'baz' => 'deepValue' ] ] ];

        $overlay = new ArrayOverlay(1, 10, 20, $settings);
        $overlay->inject($data);

        $this->assertEquals('deepValue', $overlay->getValue());
    }

}