<?php namespace Tests\Unit\Pdf\Overlays;

use App\Models\Document;
use App\Models\File;
use App\Models\Pipeline;
use App\Models\Product;
use App\Models\User;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Tests\Unit\Pdf\TestDocumentSeeder;
use TypeError;
use Upnovation\Easyprofile\Pdf\Overlays\ObjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\OverlayResolver;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperSeeder;

class TextOverlayTest extends TestCase
{
    public function provider()
    {
        return [
            ['a', 'a'],
            ['', ''],
            [1, 1],
            ['', null],
            ['string1', "string" . 1],
            // Test accent marks and special characters
            ['à', 'à'],
            ['è', 'è'],
            ['ì', 'ì'],
            ['ò', 'ò'],
            ['ù', 'ù'],
            ['Café', 'Café'],
            ['José', 'José'],
            ['François', 'François'],
        ];
    }

    /**
     * @dataProvider provider
     */
    public function testItWorks($expectation, $value)   
    {
        $overlay = new TextOverlay(1, 30, 90, [], $value);

        $this->assertEquals($expectation, $overlay->getValue());
    }
}