<script>
import RadioButton from 'primevue/radiobutton';
import InputNumber from 'primevue/inputnumber';
import FormMessage from './FormMessage.vue';

export default {
    props: {
        healthData: Object,
        errors: Object,
    },

    components: {
        RadioButton,
        InputNumber,
        FormMessage,
    },

    computed: {
        filteredErrors() {
            if (! this.errors || ! this.errors.messages || ! this.errors.key) {
                return {};
            }

            return Object.fromEntries(
                Object.entries(this.errors.messages).filter(([key]) =>
                    key.startsWith(this.errors.key)
                )
            );
        }
    },

    methods: {
        hasErrors() {
            return this.filteredErrors && Object.keys(this.filteredErrors).length;
        },
    }
}
</script>

<template>
    <div class="mb-8" :class="{'error-border rounded-lg p-5': hasErrors()}">
        <div class="font-semibold leading-6 text-gray-900 mb-3"><PERSON><PERSON>, negli ultimi 5 anni, una diagnosi o prescrizione di esami, cure,
            trattamenti, subito interventi chirurgici o assunto farmaci in merito alle
            seguenti patologie?</div>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex-grow">1. Ipertensione arteriosa (pressione superiore a 145/90 o trattamento con 2 o più medicinali)</div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute1" inputId="sal1-1" name="salute1" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal1-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute1" inputId="sal1-0" name="salute1" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal1-0">No</label>
            </div>
        </div>
        <FormMessage :errors="filteredErrors" :field="`${errors?.key}.salute1`"></FormMessage>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex-grow">2. Ictus, attacco ischemico transitorio (mini-ictus), emorragia cerebrale</div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute2" inputId="sal2-1" name="salute2" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal2-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute2" inputId="sal2-0" name="salute2" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal2-0">No</label>
            </div>
        </div>
        <FormMessage :errors="filteredErrors" :field="`${errors?.key}.salute2`"></FormMessage>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex-grow">3. Infarto, cardiopatia ischemica/coronarica, arteriosclerosi</div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute3" inputId="sal3-1" name="salute3" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal3-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute3" inputId="sal3-0" name="salute3" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal3-0">No</label>
            </div>
        </div>
        <FormMessage :errors="filteredErrors" :field="`${errors?.key}.salute3`"></FormMessage>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex-grow">4. Aritmia cardiaca tale da richiedere un trattamento farmacologico e/o controlli periodici</div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute4" inputId="sal4-1" name="salute4" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal4-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute4" inputId="sal4-0" name="salute4" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal4-0">No</label>
            </div>
        </div>
        <FormMessage :errors="filteredErrors" :field="`${errors?.key}.salute4`"></FormMessage>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex-grow">5. Qualsiasi forma di neoplasia maligna, inclusi cancro alla pelle (melanoma), leucemie, linfomi, mieloma, tumore del midollo osseo ed inoltre meningioma, nonché cisti o crescita benigna delle meningi all’interno del cervello o della spina dorsale</div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute5" inputId="sal5-1" name="salute5" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal5-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute5" inputId="sal5-0" name="salute5" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal5-0">No</label>
            </div>
        </div>
        <FormMessage :errors="filteredErrors" :field="`${errors?.key}.salute5`"></FormMessage>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex-grow">6. Immunodeficienza acquisita (AIDS) e qualsiasi altra immunopatologia che comporti deficit del sistema immunitario</div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute6" inputId="sal6-1" name="salute6" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal6-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute6" inputId="sal6-0" name="salute6" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal6-0">No</label>
            </div>
        </div>
        <FormMessage :errors="filteredErrors" :field="`${errors?.key}.salute6`"></FormMessage>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex-grow">7. Sclerosi laterale amiotrofica, sclerosi multipla, morbo di Alzheimer, morbo di Parkinson e tutte le malattie neurogenerative</div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute7" inputId="sal7-1" name="salute7" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal7-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute7" inputId="sal7-0" name="salute7" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal7-0">No</label>
            </div>
        </div>
        <FormMessage :errors="filteredErrors" :field="`${errors?.key}.salute7`"></FormMessage>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex-grow">8. Discopatia, osteoartrite o artrite infiammatoria estesa ad una o più articolazioni</div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute8" inputId="sal8-1" name="salute8" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal8-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute8" inputId="sal8-0" name="salute8" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal8-0">No</label>
            </div>
        </div>
        <FormMessage :errors="filteredErrors" :field="`${errors?.key}.salute8`"></FormMessage>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex-grow">9. Broncopneumopatia cronica ostruttiva, l’asma moderato/severo, la sarcoidosi e l’enfisema</div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute9" inputId="sal9-1" name="salute9" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal9-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute9" inputId="sal9-0" name="salute9" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal9-0">No</label>
            </div>
        </div>
        <FormMessage :errors="filteredErrors" :field="`${errors?.key}.salute9`"></FormMessage>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex-grow">10. Insufficienza renale acuta o cronica, malattie infiammatorie croniche intestinali, pancreatite cronica</div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute10" inputId="sal10-1" name="salute10" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal10-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute10" inputId="sal10-0" name="salute10" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal10-0">No</label>
            </div>
        </div>
        <FormMessage :errors="filteredErrors" :field="`${errors?.key}.salute10`"></FormMessage>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex-grow">11. Ogni forma di diabete</div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute11" inputId="sal11-1" name="salute11" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal11-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute11" inputId="sal11-0" name="salute11" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal11-0">No</label>
            </div>
        </div>
        <FormMessage :errors="filteredErrors" :field="`${errors?.key}.salute11`"></FormMessage>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex-grow">12. Cirrosi da qualsiasi causa, epatite B e C e steatosi epatica o qualsiasi epatopatia</div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute12" inputId="sal12-1" name="salute12" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal12-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute12" inputId="sal12-0" name="salute12" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal12-0">No</label>
            </div>
        </div>
        <FormMessage :errors="filteredErrors" :field="`${errors?.key}.salute12`"></FormMessage>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex-grow">13. Le seguenti Malattie o malformazioni congenite/ereditarie: idrocefalo, pneumopatia fibrocistica, cardiopatie congenite, spina bifida, atresie di organi addominali, trisomie, agenesie renali, malformazioni vascolari endocraniche, malformazioni dell’apparato urinario non corrette chirurgicamente, malformazioni dei grossi vasi</div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute13" inputId="sal13-1" name="salute13" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal13-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute13" inputId="sal13-0" name="salute13" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal13-0">No</label>
            </div>
        </div>
        <FormMessage :errors="filteredErrors" :field="`${errors?.key}.salute13`"></FormMessage>
        <div class="font-semibold leading-6 text-gray-900 mt-5 mb-3">B. È a conoscenza di essere affetto da una delle malattie di cui al punto A, o di
            una loro possibile insorgenza, di essere in attesa di risultati di consulti
            medici, esami medici o ricoveri ospedalieri (o in istituti di cura) sempre in
            riferimento alle patologie elencate al punto A?
        </div>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute14" inputId="sal14-1" name="salute14" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal14-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute14" inputId="sal14-0" name="salute14" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal14-0">No</label>
            </div>
        </div>
        <div class="font-semibold leading-6 text-gray-900 mt-5 mb-3">C. È titolare di una pensione di invalidità o inabilità (parziale o totale) o ha in
            corso pratiche per il relativo riconoscimento?</div>
        <div class="flex items-center gap-5 mb-2">
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute15" inputId="sal15-1" name="salute15" value="1" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal15-1">Si</label>
            </div>
            <div class="flex items-center gap-1">
                <RadioButton v-model="healthData.salute15" inputId="sal15-0" name="salute15" value="0" />
                <label class="block text-sm font-medium leading-6 text-gray-900" for="sal15-0">No</label>
            </div>
        </div>
    </div>
</template>

<style scoped>
.error-border {
    border: 2px solid #ef4444;
}
</style>
