<?php

use App\Models\File;
use Upnovation\Easyprofile\Pdf\Overlays\AddressOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\RadioOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\ArrayOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\ArrayMergeOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

return [
    'code' => 'AXASIMPLYPR',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates/products',
        'filename' => 'axa-simply-protection-v1.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'product-form',
        'title' => 'AXA Simply Protection',
        'version' => '1.0.0',
        'description' => "Scheda Adesione AXA Simply Protection",
        'processor' => PdfProcessor::class,
        'signers' => ["contractor"],    
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ],
        ],

        //
        // This is not stored in the database anymore.
        //
        'overlayArray' => [
            //new UserOverlay(1, 40, 42, ['properties' => ['name', 'lastname'],]),
            //new TextOverlay(1, 144, 42, [], 'ciao ciao'),
            new ArrayOverlay(1, 169, 53.5, [
                'key' => 'moduloAdesione'
            ]),
            new ArrayOverlay(1, 110, 68.5, [
                'key' => 'capitaleAssicurato',
                'fmtCurrency' => true
            ]),
            new ArrayOverlay(1, 110, 76, [
                'key' => 'indennitaMensile',
                'fmtCurrency' => true
            ]),
            new ArrayOverlay(1, 44, 96, [
                'key' => 'datiAssicurato.localita'
            ]),
            new SubjectOverlay(1, 50, 92, [
                'role' => 'contractor',
                'properties' => ['name', 'lastname'],
                'separator' => ' ',
            ]),
            new SubjectOverlay(1, 130, 92, [
                'role' => 'contractor',
                'properties' => ['taxCode'],
            ]),
            new SubjectOverlay(1, 45, 97, [
                'role' => 'contractor',
                'properties' => ['birthdate'],
            ]),
            new SubjectOverlay(1, 135, 96.5, [
                'role' => 'contractor',
                'properties' => ['birthplaceCity'],
            ]),
            new SubjectOverlay(1, 35, 119.5, [
                'role' => 'contractor',
                'properties' => ['phone'],
            ]),
            new SubjectOverlay(1, 117, 119.5, [
                'role' => 'contractor',
                'properties' => ['email'],
            ]),
            new RadioOverlay(1, 0, 0, [
                'key' => 'pacchetto',
                'options' => [
                    '1' => new TextOverlay(1, 21.3, 159.3, [], "x"),
                    '2' => new TextOverlay(1, 21.3, 179, [], "x"),
                    '3' => new TextOverlay(1, 21.3, 196.5, [], "x"),
                    '4' => new TextOverlay(1, 21.3, 218.2, [], "x"),
                ],
            ]),
            new RadioOverlay(1, 0, 0, [
                'key' => 'durata',
                'options' => [
                    '5' => new TextOverlay(1, 21.3, 259.1, [], "x"),
                    '10' => new TextOverlay(1, 40.8, 259.1, [], "x"),
                ],
            ]),

            /* --------- Pagina 2 ---------- */
            /* ----------------------------- */
            new ArrayOverlay(2, 97, 34.8, [
                'key' => 'premioUnico',
                'fmtCurrency' => true
            ]),
            new RadioOverlay(2, 0, 0, [
                'key' => 'modalitaPagamento',
                'options' => [
                    '1' => new TextOverlay(2, 21.3, 59, [], "x"),
                    '2' => new TextOverlay(2, 21.3, 62.8, [], "x"),
                    '3' => new TextOverlay(2, 21.3, 67.2, [], "x"),
                ],
            ]),

            /* --------- Pagina 3 ---------- */
            /* ----------------------------- */
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute1',
                'options' => [
                    '1' => new TextOverlay(3, 169, 95, [], "X"),
                    '0' => new TextOverlay(3, 182, 95, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute2',
                'options' => [
                    '1' => new TextOverlay(3, 169, 103, [], "X"),
                    '0' => new TextOverlay(3, 182, 103, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute3',
                'options' => [
                    '1' => new TextOverlay(3, 169, 111, [], "X"),
                    '0' => new TextOverlay(3, 182, 111, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute4',
                'options' => [
                    '1' => new TextOverlay(3, 169, 118, [], "X"),
                    '0' => new TextOverlay(3, 182, 118, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute5',
                'options' => [
                    '1' => new TextOverlay(3, 169, 128, [], "X"),
                    '0' => new TextOverlay(3, 182, 128, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute6',
                'options' => [
                    '1' => new TextOverlay(3, 169, 138, [], "X"),
                    '0' => new TextOverlay(3, 182, 138, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute7',
                'options' => [
                    '1' => new TextOverlay(3, 169, 146, [], "X"),
                    '0' => new TextOverlay(3, 182, 146, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute8',
                'options' => [
                    '1' => new TextOverlay(3, 169, 154, [], "X"),
                    '0' => new TextOverlay(3, 182, 154, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute9',
                'options' => [
                    '1' => new TextOverlay(3, 169, 162, [], "X"),
                    '0' => new TextOverlay(3, 182, 162, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute10',
                'options' => [
                    '1' => new TextOverlay(3, 169, 170, [], "X"),
                    '0' => new TextOverlay(3, 182, 170, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute11',
                'options' => [
                    '1' => new TextOverlay(3, 169, 178, [], "X"),
                    '0' => new TextOverlay(3, 182, 178, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute12',
                'options' => [
                    '1' => new TextOverlay(3, 169, 186, [], "X"),
                    '0' => new TextOverlay(3, 182, 186, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute12',
                'options' => [
                    '1' => new TextOverlay(3, 169, 198, [], "X"),
                    '0' => new TextOverlay(3, 182, 198, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute13',
                'options' => [
                    '1' => new TextOverlay(3, 169, 214, [], "X"),
                    '0' => new TextOverlay(3, 182, 214, [], "X")
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute13',
                'options' => [
                    '1' => new TextOverlay(3, 169, 227, [], "X"),
                    '0' => new TextOverlay(3, 182, 227, [], "X")
                ],
            ]),

            /* --------- Pagina 4 ---------- */
            /* ----------------------------- */
            new RadioOverlay(4, 0, 0, [
                'key' => 'beneficiario',
                'options' => [
                    'nomina' => new TextOverlay(4, 21, 154, [], "X"),
                    'eredi' => new TextOverlay(4, 21, 261.8, [], "X")
                ],
            ]),
            new ArrayMergeOverlay(4, 48, 167, [
                'keys' => ['beneficiarioDesignato1.nome', 'beneficiarioDesignato1.cognome'],
                'separator' => ' ', // or any other separator like '', '-', '/', etc.
            ]),
            new ArrayOverlay(4, 42, 171.2, [
                'key' => 'beneficiarioDesignato1.cf'
            ]),
            new ArrayOverlay(4, 35, 175.4, [
                'key' => 'beneficiarioDesignato1.indirizzo'
            ]),
            new ArrayOverlay(4, 35, 179.6, [
                'key' => 'beneficiarioDesignato1.telefono'
            ]),
            new ArrayOverlay(4, 80, 179.6, [
                'key' => 'beneficiarioDesignato1.email'
            ]),
            new RadioOverlay(4, 0, 0, [
                'key' => 'beneficiarioDesignato1.relazione',
                'options' => [
                    'familiare' => new TextOverlay(4, 21.3, 193, [], "x"),
                    'altro' => new TextOverlay(4, 151.6, 193, [], "x")
                ],
            ]),
            new RadioOverlay(4, 0, 0, [
                'key' => 'beneficiarioDesignato1.noComunicazione',
                'options' => [
                    true => new TextOverlay(4, 81, 201.5, [], "x"),
                    false => new TextOverlay(4, 81, 201.5, [], "")
                ],
            ]),
            new ArrayMergeOverlay(4, 48, 214, [
                'keys' => ['beneficiarioDesignato2.nome', 'beneficiarioDesignato2.cognome'],
                'separator' => ' ',
            ]),
            new ArrayOverlay(4, 42, 218.5, [
                'key' => 'beneficiarioDesignato2.cf'
            ]),
            new ArrayOverlay(4, 35, 222.8, [
                'key' => 'beneficiarioDesignato2.indirizzo'
            ]),
            new ArrayOverlay(4, 35, 227.5, [
                'key' => 'beneficiarioDesignato2.telefono'
            ]),
            new ArrayOverlay(4, 80, 227, [
                'key' => 'beneficiarioDesignato2.email'
            ]),
            new RadioOverlay(4, 0, 0, [
                'key' => 'beneficiarioDesignato2.relazione',
                'options' => [
                    'familiare' => new TextOverlay(4, 21.3, 240.5, [], "x"),
                    'altro' => new TextOverlay(4, 151.6, 240.5, [], "x")
                ],
            ]),
            new RadioOverlay(4, 0, 0, [
                'key' => 'beneficiarioDesignato2.noComunicazione',
                'options' => [
                    true => new TextOverlay(4, 81, 249.1, [], "x"),
                    false => new TextOverlay(4, 81, 201.5, [], "")
                ],
            ]),

            /* --------- Pagina 5 ---------- */
            /* ----------------------------- */
            new ArrayMergeOverlay(5, 75, 45, [
                'keys' => ['referenteTerzo.nome', 'referenteTerzo.cognome'],
                'separator' => ' ',
            ]),
            new ArrayOverlay(5, 54, 49.6, [
                'key' => 'referenteTerzo.indirizzo'
            ]),
            new ArrayOverlay(5, 35, 54, [
                'key' => 'referenteTerzo.telefono'
            ]),
            new ArrayOverlay(5, 102, 54, [
                'key' => 'referenteTerzo.email'
            ]),

            /* --------- Pagina 6 ---------- */
            /* ----------------------------- */
            new SubjectOverlay(6, 40, 175, [
                'role' => 'contractor',
                'properties' => ['name'],
            ]),
            new SubjectOverlay(6, 124, 175, [
                'role' => 'contractor',
                'properties' => ['lastname'],
            ]),
            new SubjectOverlay(6, 52, 180, [
                'role' => 'contractor',
                'properties' => ['birthdate'],
            ]),
            new SubjectOverlay(6, 130, 180, [
                'role' => 'contractor',
                'properties' => ['taxCode'],
            ]),
            new SubjectOverlay(6, 57, 185, [
                'role' => 'contractor',
                'properties' => ['birthplaceCity'],
            ]),

            /* --------- Pagina 7 ---------- */
            /* ----------------------------- */
            new RadioOverlay(7, 0, 0, [
                'key' => 'PEP',
                'options' => [
                    '0' => new TextOverlay(7, 76.5, 50, [], "x"),
                    '1' => new TextOverlay(7, 64.6, 50, [], "x")
                ],
            ]),
            new ArrayOverlay(7, 75, 54.5, [
                'key' => 'PEPType'
            ]),
            new RadioOverlay(7, 0, 0, [
                'key' => 'congelamentoFondi',
                'options' => [
                    '0' => new TextOverlay(7, 114.5, 68, [], "x"),
                    '1' => new TextOverlay(7, 122.5, 68, [], "x")
                ],
            ]),
            new ArrayOverlay(7, 74, 72, [
                'key' => 'congelamentoFondiMotivo'
            ]),
            new RadioOverlay(7, 0, 0, [
                'key' => 'precedentiPenali',
                'options' => [
                    '0' => new TextOverlay(7, 143.5, 85.5, [], "x"),
                    '1' => new TextOverlay(7, 151.5, 85.5, [], "x")
                ],
            ]),
            new ArrayOverlay(7, 62, 90, [
                'key' => 'precedentiPenaliMotivo'
            ]),
            new RadioOverlay(7, 0, 0, [
                'key' => 'origineFondi',
                'options' => [
                    'proprie' => new TextOverlay(7, 44.3, 171.7, [], "x"),
                    'eredita' => new TextOverlay(7, 44.3, 176, [], "x"),
                    'donazione' => new TextOverlay(7, 44.3, 180, [], "x"),
                    'altro' => new TextOverlay(7, 44.3, 184.5, [], "x"),
                ],
            ]),
            new ArrayOverlay(7, 75, 184.5, [
                'key' => 'origineFondiAltro'
            ]),

            /* --------- Pagina 10 --------- */
            /* ----------------------------- */
            new SubjectOverlay(10, 20, 64, [
                'role' => 'contractor',
                'properties' => ['name'],
            ]),
            new SubjectOverlay(10, 80, 64, [
                'role' => 'contractor',
                'properties' => ['lastname'],
            ]),
            new SubjectOverlay(10, 136, 64, [
                'role' => 'contractor',
                'properties' => ['birthdate'],
            ]),
            new RadioOverlay(10, 0, 0, [
                'key' => 'esigenze2_1',
                'options' => [
                    '0' => new TextOverlay(10, 17, 234.5, [], "X"),
                    '1' => new TextOverlay(10, 9.5, 234.5, [], "X"),
                ],
            ]),
            new RadioOverlay(10, 0, 0, [
                'key' => 'esigenze2_2',
                'options' => [
                    '0' => new TextOverlay(10, 17, 269, [], "X"),
                    '1' => new TextOverlay(10, 9.5, 269, [], "X"),
                ],
            ]),

            /* --------- Pagina 11 --------- */
            /* ----------------------------- */
            new RadioOverlay(11, 0, 0, [
                'key' => 'esigenze3',
                'options' => [
                    '0' => new TextOverlay(11, 21, 54, [], "X"),
                    '1' => new TextOverlay(11, 9.5, 54, [], "X"),
                ],
            ]),
            new RadioOverlay(11, 0, 0, [
                'key' => 'esigenze4',
                'options' => [
                    '0' => new TextOverlay(11, 21, 88.5, [], "X"),
                    '1' => new TextOverlay(11, 9.5, 88.5, [], "X"),
                ],
            ]),
            new RadioOverlay(11, 0, 0, [
                'key' => 'esigenze5',
                'options' => [
                    '0' => new TextOverlay(11, 21, 127.2, [], "X"),
                    '1' => new TextOverlay(11, 9.5, 127.2, [], "X"),
                ],
            ]),
            new RadioOverlay(11, 0, 0, [
                'key' => 'esigenze6',
                'options' => [
                    '0' => new TextOverlay(11, 22.5, 166, [], "X"),
                    '1' => new TextOverlay(11, 9.5, 166, [], "X"),
                ],
            ]),
        ],
    ],

    // @FIXME! per prodotti direct creare direttamente in fase di install
    // un product-policy come copia esatta del product-form, perché sono di fatto
    // la stessa cosa.
    'policy' => [
        'title' => 'AXA Simply Protection - Proposta di adesione',
        'signers' => ["contractor"], 
        'description' => "Proposta assicurativa da firmare.",
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ]
        ],
    ],

    //
    // This defines the form data to be collected for this product and THIS document.
    // Defines the validation rules.
    //
    'formRules' =>[
        
    ],
];