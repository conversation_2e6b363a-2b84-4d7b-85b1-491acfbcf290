<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Document;
use App\Models\File;
use App\Models\Person;
use App\Models\Pipeline;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Upnovation\Easyprofile\Modules\Documents\DocumentsInstaller;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;
use Tests\Unit\Pdf\TestDocumentSeeder;

class PdfToolsController extends Controller
{
    protected DocumentsInstaller $installer;

    protected PdfProcessor $pdfProcessor;

    public function __construct(DocumentsInstaller $installer, PdfProcessor $pdfProcessor)
    {
        $this->installer = $installer;

        $this->pdfProcessor = $pdfProcessor;
    }

    public function getDocument(Document $document, ?Pipeline $pipeline = null)
    {
        if (! $config = $document->loadConfiguration()) {
            dd("Document configuration for {$document->title} not found.");
        }

        if (! file_exists($config['file']->getFullPath())) {
            dd("Document file for {$document->title} not found.");
        }

        // invoke seeder
        if (! $pipeline) {
            $pipeline = (new TestDocumentSeeder())->makePipeline();
        }

        // Disabled: there's a fix in Document model that forces overlays
        // to be read from configuration.
        //$document->overlayArray = $config['document']['overlayArray'];

        $outfile = $this->pdfProcessor->compile(
            $document,
            $pipeline,
            new File([
                'disk' => 'documents',
                'path' => 'compiled',
                'filename' => 'test-' . $config['file']->filename,
            ]),
            [
                'datiAssicurato' => [
                    'nome' => 'Mario',
                    'cognome' => 'Debug',
                    'via' => 'Via Test',
                    'numVia' => '1',
                    'cap' => '20100',
                    'localita' => 'Milano',
                    'provincia' => 'MI',
                    'telefono' => '**********',
                    'email' => '<EMAIL>'
                ],
                'moduloAdesione' => 123456,
                'capitaleAssicurato' => 125000,
                'indennitaMensile' => 1200,
                'pacchetto' => 4,
                'durata' => '10',
                'premioUnico' => 1000,
                'modalitaPagamento' => '3',
                'healthData' => [
                    'salute1' => '0',
                    'salute2' => '0',
                    'salute3' => '0',
                    'salute4' => '0',
                    'salute5' => '0',
                    'salute6' => '0',
                    'salute7' => '0',
                    'salute8' => '0',
                    'salute9' => '0',
                    'salute10' => '0',
                    'salute11' => '0',
                    'salute12' => '0',
                    'salute13' => '0',
                    'salute14' => '0',
                    'salute15' => '0',
                ],
                'beneficiario' => 'nomina',
                'beneficiarioDesignato1' => [
                    'nome' => 'Mario',
                    'cognome' => 'Rossi',
                    'cf' => '****************',
                    'indirizzo' => 'Via Roma 123',
                    'telefono' => '**********',
                    'email' => '<EMAIL>',
                    'relazione' => 'altro',
                    'noComunicazione' => true,
                ],
                'beneficiarioDesignato2' => [
                    'nome' => 'Mario',
                    'cognome' => 'Rossi',
                    'cf' => '****************',
                    'indirizzo' => 'Via Roma 123',
                    'telefono' => '**********',
                    'email' => '<EMAIL>',
                    'relazione' => 'familiare',
                    'noComunicazione' => true,
                ],
                'referenteTerzo' => [
                    'nome' => 'Mario',
                    'cognome' => 'Rossi',
                    'indirizzo' => 'Via Roma 123',
                    'telefono' => '**********',
                    'email' => '<EMAIL>',
                ],
                'PEP' => '1',
                'PEPType' => 'testo PEP',
                'congelamentoFondi' => '1',
                'congelamentoFondiMotivo' => 'testo congelamento fondi',
                'precedentiPenali' => '1',
                'precedentiPenaliMotivo' => 'testo precedenti penali',
                'personaFisica' => '0',
                'origineFondi' => 'altro',
                'origineFondiAltro' => 'testo origine fondi',
                'esigenze2_1' => '1',
                'esigenze2_2' => '1',
                'esigenze3' => '0',
                'esigenze4' => '0',
                'esigenze5' => '0',
                'esigenze6' => '0',
            ]

        );

        return response()->file($outfile->getFullPath());
    }
}

