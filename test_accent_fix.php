<?php

// Simple test to verify accent mark encoding fix
require_once 'vendor/autoload.php';

use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;

echo "Testing accent mark handling...\n";

// Test cases with accent marks
$testCases = [
    'à' => 'à',
    'è' => 'è', 
    'ì' => 'ì',
    'ò' => 'ò',
    'ù' => 'ù',
    'Café' => 'Café',
    'José' => 'José',
    'François' => '<PERSON>',
    'Müller' => 'Müller',
    'Björk' => 'Björk'
];

foreach ($testCases as $input => $expected) {
    $overlay = new TextOverlay(1, 30, 90, [], $input);
    $result = $overlay->getValue();
    
    // Test the encoding conversion that happens in PdfProcessor::print()
    $encodedResult = mb_convert_encoding($result, 'UTF-8', 'UTF-8');
    
    $status = ($encodedResult === $expected) ? "✓ PASS" : "✗ FAIL";
    echo sprintf("%-10s | Input: %-10s | Expected: %-10s | Got: %-10s\n", 
        $status, $input, $expected, $encodedResult);
}

echo "\nTest completed!\n";
