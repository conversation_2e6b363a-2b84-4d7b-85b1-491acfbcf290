<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

use App\Models\Pipeline;
use Illuminate\Support\Facades\Log;
use Upnovation\Easyprofile\Pdf\Overlays\Traits\MultilevelPropertyAccess;

class ArrayMergeOverlay extends AbstractOverlay implements InjectableInterface
{
    use MultilevelPropertyAccess;

    public function __construct($page, $x, $y, array $settings)
    {
        parent::__construct($page, $x, $y);
     
        if (empty($settings) || empty($settings['keys'])) {
            throw new \Exception("Settings must contain 'keys' array for ArrayMergeOverlay");
        }

        if (!is_array($settings['keys'])) {
            throw new \Exception("'keys' setting must be an array for ArrayMergeOverlay");
        }

        $this->settings = $settings;
    }

    public function inject($data)
    {
        $this->value = (array)$data;
    }

    public function getValue(): string
    {
        $keys = $this->settings['keys'];
        $separator = $this->settings['separator'] ?? ' ';
        $values = [];

        foreach ($keys as $key) {
            $value = $this->resolveMultilevelProperty($key, $this->value, 'ArrayMergeOverlay');
            
            // Only add non-empty values to the result
            if ($value !== "" && !is_null($value)) {
                $values[] = $value;
            }
        }

        // If no values were found, return empty string
        if (empty($values)) {
            return "";
        }

        $result = join($separator, $values);

        return $result;
    }

    public function resolve(Pipeline $pipeline)
    {
        // Do nothing. Work with injected data.
    }
}
