<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

use Upnovation\Easyprofile\Pdf\Overlays\Traits\MultilevelPropertyAccess;

class RadioOverlay extends ArrayOverlay
{
    use MultilevelPropertyAccess;

    public function __construct($page, $x, $y, array $settings)
    {
        parent::__construct($page, $x, $y, $settings);

        if (empty($settings['options'])) {
            throw new \Exception("Options cannot be empty for RadioOverlay");
        }

        $this->settings['options'] = $settings['options'];
    }

    public function getValue() : string
    {
        // Invoke parent just to assert the conditions are met.
        parent::getValue();

        $key = $this->settings['key'];
        \Log::info("DEBUG RadioOverlay: Looking for key: '{$key}'");
        \Log::info("DEBUG RadioOverlay: Available data keys: " . implode(', ', array_keys($this->value)));

        $optionKey = $this->resolveMultilevelProperty($key, $this->value, 'RadioOverlay');

        \Log::info("DEBUG RadioOverlay: Resolved option key: '{$optionKey}' (type: " . gettype($optionKey) . ")");
        \Log::info("DEBUG RadioOverlay: Available option keys: " . implode(', ', array_keys($this->settings['options'])));

        if ($optionKey === "" || $optionKey === null || empty($optionKey)) {
            throw new \Exception("Invalid key '{$key}' for RadioOverlay - resolved to: " . var_export($optionKey, true));
        }

        /** @var AbstractOverlay */
        $option = $this->settings['options'][$optionKey];

        // Override main overlay coordinates with the option's coordinates.
        $this->page = $option->page;
        $this->x = $option->x;
        $this->y = $option->y;

        return $option->getValue();
    }

}