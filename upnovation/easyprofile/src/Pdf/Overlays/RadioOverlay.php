<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

use Upnovation\Easyprofile\Pdf\Overlays\Traits\MultilevelPropertyAccess;

class RadioOverlay extends ArrayOverlay
{
    use MultilevelPropertyAccess;

    public function __construct($page, $x, $y, array $settings)
    {
        parent::__construct($page, $x, $y, $settings);

        if (empty($settings['options'])) {
            throw new \Exception("Options cannot be empty for RadioOverlay");
        }

        $this->settings['options'] = $settings['options'];
    }

    public function getValue() : string
    {
        // Invoke parent just to assert the conditions are met.
        parent::getValue();

        $key = $this->settings['key'];
        $optionKey = $this->resolveMultilevelProperty($key, $this->value, 'RadioOverlay');

        if ($optionKey === "") {
            throw new \Exception("Invalid key '{$key}' for RadioOverlay");
        }

        /** @var AbstractOverlay */
        $option = $this->settings['options'][$optionKey];

        // Override main overlay coordinates with the option's coordinates.
        $this->page = $option->page;
        $this->x = $option->x;
        $this->y = $option->y;

        return $option->getValue();
    }

}