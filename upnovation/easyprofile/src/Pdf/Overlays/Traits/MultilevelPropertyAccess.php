<?php namespace Upnovation\Easyprofile\Pdf\Overlays\Traits;

use Illuminate\Support\Facades\Log;

trait MultilevelPropertyAccess
{
    /**
     * Resolves a multilevel property key (e.g., "foo.bar.baz") from an array value.
     * 
     * @param string $key The property key, potentially with dot notation
     * @param array $value The array to search in
     * @param string $context Context for logging (e.g., class name)
     * @return mixed The resolved value or empty string if not found
     * @throws \Exception If a key part is empty
     */
    protected function resolveMultilevelProperty(string $key, array $value, string $context = 'MultilevelPropertyAccess')
    {
        $parts = array_filter(explode('.', $key));

        if (empty($parts)) {
            Log::warning("Key {$key} is empty for {$context}");
            return "";
        }

        $currentValue = $value;

        foreach ($parts as $part) {
            if (! is_array($currentValue) || ! array_key_exists($part, $currentValue)) {
                Log::warning("Key {$key} does not exist in value for {$context}");
                return "";
            }

            $part = trim($part);

            if (! $part || $part == "") {
                throw new \Exception("Key part is empty for {$context}");
            }
            
            $currentValue = $currentValue[$part];
        }

        return $currentValue;
    }
}