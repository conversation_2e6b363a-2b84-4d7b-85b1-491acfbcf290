<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

use App\Models\Pipeline;
use Illuminate\Support\Facades\Log;
use Upnovation\Easyprofile\Pdf\Overlays\Traits\MultilevelPropertyAccess;

class ArrayOverlay extends AbstractOverlay implements InjectableInterface
{
    use MultilevelPropertyAccess;
    public function __construct($page, $x, $y, array $settings)
    {
        parent::__construct($page, $x, $y);
     
        if (empty($settings) || empty($settings['key'])) {
            throw new \Exception("Settings cannot be empty for ArrayOverlay");
        }

        $this->settings = $settings;
    }

    public function inject($data)
    {
        $this->value = (array)$data;
    }


	public function getValue() : string
	{
        $key = $this->settings['key'];
        $value = $this->resolveMultilevelProperty($key, $this->value, 'ArrayOverlay');

        // If the key wasn't found, return empty string
        if ($value === "") {
            dd($key);
            return "";
        }

if (
    isset($this->settings['fmtCurrency']) &&
    $this->settings['fmtCurrency'] === true &&
    is_numeric($value)
) {
    return number_format((float)$value, 2, ',', '.');
}

if (
    isset($this->settings['fmtDate']) &&
    $this->settings['fmtDate'] === true &&
    is_string($value) &&
    preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)
) {
    $parts = explode('-', $value);
    if (count($parts) === 3) {
        return $parts[2] . '-' . $parts[1] . '-' . $parts[0];
    }
}

        return is_null($value) ? "" : $value;
    }

	public function resolve(Pipeline $pipeline)
    {
        // Do nothing. Work with injected data.
    }

}