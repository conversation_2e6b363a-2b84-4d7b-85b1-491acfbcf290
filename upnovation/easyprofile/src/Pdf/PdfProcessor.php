<?php namespace Upnovation\Easyprofile\Pdf;

use App\Models\Document;
use App\Models\File;
use App\Models\Pipeline;
use Exception;
use Illuminate\Support\Facades\Log;
use setasign\Fpdi\Fpdi;
use Upnovation\Easyprofile\Pdf\Overlays\AbstractOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\OverlayResolver;

class PdfProcessor implements PdfProcessorInterface
{
    //
    // Overlays todo list
    // - string
    // - user
    // - person
    // - enterprise
    // - profile
    // ...

    public function compile(Document $document, Pipeline $pipeline, File $outfile, $data = null) : File
    {
        $resolver = new OverlayResolver($data);

        $document->overlays = $resolver->resolve($document->getOverlayArray(), $pipeline);

        return $this->compileDocument($document, $outfile);
    }

    public function compileDocument(Document $document, File $file)
    {
        $pdf = new Fpdi();

        $pageCount = $pdf->setSourceFile($document->template->getFullPath());

        // Index by page.
        $overlays = collect($document->getOverlayArray())->groupBy('page')->all();
        
        for ($i = 1; $i <= $pageCount; $i++) {
            // Setup page
            $templateId = $pdf->importPage($i);
            $size = $pdf->getTemplateSize($templateId);
            $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
            $pdf->useTemplate($templateId);

            if (! isset($overlays[$i])) {
                // No overlays for this page, continue to next
                continue;
            }

            // @todo
            // Use Arial which has better support for extended characters
            $pdf->SetFont('Arial', '', 11);
            //$pdf->SetTextColor();

            foreach ($overlays[$i] as $overlay) {
                $this->print($overlay, $pdf);
            }
        }

        $filename = $file->getFullPath();

        try {
            $pdf->Output('F', $filename);

            if (! file_exists($filename)) {
                throw new Exception("Cannot create file: $filename");
            }

            return $file;
        } catch (Exception $e) {
            Log::error("Error while compiling document: {$e->getMessage()}");

            throw new Exception("Cannot create sample file: {$e->getMessage()}");
        }
    }

    public function print(AbstractOverlay $overlay, Fpdi $pdf)
    {
        /*$pdf->SetFont($overlay['fontFamily'] ?? 'Helvetica');
        $pdf->SetFontSize($overlay['fontSize'] ?? 11);
        $pdf->SetTextColor(...$overlay['fontColor']);*/

        // It's important to fetch the value before setting the position
        // because the RadioOverlay options will override the position.
        $value = $overlay->getValue();

        // Convert encoding to handle accent marks and special characters
        $value = mb_convert_encoding($value, 'UTF-8', 'UTF-8');

        $pdf->SetXY(
            $overlay->x,
            $overlay->y
        );

        $pdf->Write(0, $value);
    }

    
}